{"name": "dhr-lining-mock", "version": "1.0.0", "description": "李宁劳动力预测Mock数据生成和导入工具 - 为data agent的劳动力预测提供完整的数据支撑，实现门店未来销售、客流、人力配置的智能预测。", "main": "mock-data-generator.js", "scripts": {"generate": "node mock-data-generator.js", "analyze": "node data-analysis.js", "validate": "node validate-data.js", "import": "node import-data.js", "install-deps": "npm install mysql2", "setup": "npm run install-deps && npm run generate", "full-import": "npm run generate && npm run import", "help": "echo '可用命令:\\n  npm run generate - 生成Mock数据\\n  npm run analyze - 分析数据\\n  npm run validate - 验证数据完整性\\n  npm run import - 导入数据到数据库\\n  npm run setup - 安装依赖并生成数据\\n  npm run full-import - 生成数据并导入数据库'"}, "keywords": ["mock-data", "lining", "labor-prediction", "retail-analytics", "workforce-planning"], "author": "DHR Data Analysis Team", "license": "MIT", "dependencies": {"mysql2": "^3.14.2"}, "engines": {"node": ">=14.0.0"}}