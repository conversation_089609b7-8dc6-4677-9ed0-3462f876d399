hoistPattern:
  - '*'
hoistedDependencies:
  '@fast-csv/format@4.3.5':
    '@fast-csv/format': private
  '@fast-csv/parse@4.3.6':
    '@fast-csv/parse': private
  '@types/node@14.18.63':
    '@types/node': private
  aws-ssl-profiles@1.1.2:
    aws-ssl-profiles: private
  denque@2.1.0:
    denque: private
  generate-function@2.3.1:
    generate-function: private
  iconv-lite@0.6.3:
    iconv-lite: private
  is-property@1.0.2:
    is-property: private
  lodash.escaperegexp@4.1.2:
    lodash.escaperegexp: private
  lodash.groupby@4.6.0:
    lodash.groupby: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isequal@4.5.0:
    lodash.isequal: private
  lodash.isfunction@3.0.9:
    lodash.isfunction: private
  lodash.isnil@4.0.0:
    lodash.isnil: private
  lodash.isundefined@3.0.1:
    lodash.isundefined: private
  lodash.uniq@4.5.0:
    lodash.uniq: private
  long@5.3.2:
    long: private
  lru-cache@7.18.3:
    lru-cache: private
  lru.min@1.1.2:
    lru.min: private
  named-placeholders@1.1.3:
    named-placeholders: private
  safer-buffer@2.1.2:
    safer-buffer: private
  seq-queue@0.0.5:
    seq-queue: private
  sqlstring@2.3.3:
    sqlstring: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.6.1
pendingBuilds: []
prunedAt: Mon, 28 Jul 2025 03:36:15 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
