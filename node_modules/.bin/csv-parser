#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/deloitte/04.DHR-资产/code/dhr-lining-mock/node_modules/.pnpm/csv-parser@3.2.0/node_modules/csv-parser/bin/node_modules:/Users/<USER>/deloitte/04.DHR-资产/code/dhr-lining-mock/node_modules/.pnpm/csv-parser@3.2.0/node_modules/csv-parser/node_modules:/Users/<USER>/deloitte/04.DHR-资产/code/dhr-lining-mock/node_modules/.pnpm/csv-parser@3.2.0/node_modules:/Users/<USER>/deloitte/04.DHR-资产/code/dhr-lining-mock/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/deloitte/04.DHR-资产/code/dhr-lining-mock/node_modules/.pnpm/csv-parser@3.2.0/node_modules/csv-parser/bin/node_modules:/Users/<USER>/deloitte/04.DHR-资产/code/dhr-lining-mock/node_modules/.pnpm/csv-parser@3.2.0/node_modules/csv-parser/node_modules:/Users/<USER>/deloitte/04.DHR-资产/code/dhr-lining-mock/node_modules/.pnpm/csv-parser@3.2.0/node_modules:/Users/<USER>/deloitte/04.DHR-资产/code/dhr-lining-mock/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../csv-parser/bin/csv-parser" "$@"
else
  exec node  "$basedir/../csv-parser/bin/csv-parser" "$@"
fi
