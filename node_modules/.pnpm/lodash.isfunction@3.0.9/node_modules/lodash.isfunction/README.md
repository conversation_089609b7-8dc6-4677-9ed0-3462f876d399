# lodash.isfunction v3.0.9

The [Lodash](https://lodash.com/) method `_.isFunction` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.isfunction
```

In Node.js:
```js
var isFunction = require('lodash.isfunction');
```

See the [documentation](https://lodash.com/docs#isFunction) or [package source](https://github.com/lodash/lodash/blob/3.0.9-npm-packages/lodash.isfunction) for more details.
