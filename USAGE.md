# 李宁劳动力预测Mock数据使用指南

## 快速开始

### 1. 安装依赖
```bash
npm run install-deps
# 或者
npm install mysql2
```

### 2. 生成Mock数据
```bash
npm run generate
```
这将生成10个CSV文件和1个SQL建表文件到 `./output/` 目录。

### 3. 验证数据质量
```bash
npm run validate
```

### 4. 分析数据
```bash
npm run analyze
```

### 5. 导入数据到数据库
```bash
npm run import
```

## 数据库配置

数据库连接信息在 `import-data.js` 中配置：

```javascript
const dbConfig = {
    host: '************',
    port: 3306,
    user: 'dhr_data_anlaysis',
    password: 'dhr_data_anlaysis',
    database: 'dhr_data_anlaysis',
    charset: 'utf8mb4'
};
```

## 表结构说明

所有表都添加了 `lining_labor_` 前缀，包含详细的字段注释：

### 核心业务表
- `lining_labor_store` - 门店信息表
- `lining_labor_product` - 商品信息表
- `lining_labor_employee` - 员工信息表
- `lining_labor_member` - 会员信息表

### 运营数据表
- `lining_labor_promotion` - 促销活动表
- `lining_labor_schedule` - 排班数据表
- `lining_labor_attendance` - 考勤记录表
- `lining_labor_traffic` - 客流数据表
- `lining_labor_transaction` - 交易数据表
- `lining_labor_forecast_history` - 历史预测记录表

## 可用命令

| 命令 | 说明 |
|------|------|
| `npm run generate` | 生成Mock数据 |
| `npm run analyze` | 分析数据统计 |
| `npm run validate` | 验证数据完整性 |
| `npm run import` | 导入数据到数据库 |
| `npm run setup` | 安装依赖并生成数据 |
| `npm run full-import` | 生成数据并导入数据库 |
| `npm run help` | 显示帮助信息 |

## 数据特征

### 业务指标
- **总销售额**: 13.29亿元 (3年累计)
- **平均客单价**: ¥1,238.41
- **客流转化率**: 12.99%
- **会员交易占比**: 40%

### 商品结构
- **鞋类**: 54个商品 (51.4%)，平均价格¥819
- **服装**: 43个商品 (41.0%)，平均价格¥375
- **器材配件**: 8个商品 (7.6%)，平均价格¥493

### 门店分布
- **上海青浦店**: 大型店，年销售额3.1亿+
- **广州天河店**: 大型店，年销售额3.1亿+
- **北京朝阳店**: 中型店，年销售额2.5亿
- **深圳南山店**: 中型店，年销售额2.0亿
- **成都锦江店**: 小型店，年销售额1.5亿

## 数据导入流程

1. **检查数据库连接**
2. **创建表结构** (如果不存在)
3. **按依赖顺序导入数据**:
   - 基础表: store → product → employee → member
   - 业务表: promotion → schedule → attendance → traffic → transaction → forecast_history
4. **验证导入结果**

## 故障排除

### 数据库连接失败
- 检查网络连接
- 确认数据库服务器地址和端口
- 验证用户名和密码
- 确保数据库存在

### 导入数据失败
- 检查表结构是否正确创建
- 确认外键约束
- 查看错误日志定位具体问题

### 数据验证失败
- 运行 `npm run validate` 检查数据完整性
- 重新生成数据: `npm run generate`

## 自定义配置

### 修改数据库连接
编辑 `import-data.js` 中的 `dbConfig` 对象。

### 调整数据规模
编辑 `mock-data-generator.js` 中的相关参数：
- 门店数量
- 商品数量
- 员工数量
- 会员数量
- 时间范围

### 修改业务逻辑
根据实际需求调整：
- 季节性因子
- 促销影响系数
- 客流转化率
- 价格区间

## 注意事项

1. **数据安全**: 本工具生成的是模拟数据，不包含真实个人信息
2. **性能考虑**: 大量数据导入可能需要较长时间，建议分批处理
3. **存储空间**: 完整数据集约占用200MB磁盘空间
4. **版本兼容**: 需要Node.js 14.0.0或更高版本

## 技术支持

如遇到问题，请检查：
1. Node.js版本是否符合要求
2. 数据库连接是否正常
3. 磁盘空间是否充足
4. 网络连接是否稳定
