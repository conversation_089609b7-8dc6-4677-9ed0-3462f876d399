const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
    host: '************',
    port: 3306,
    user: 'dhr_data_anlaysis',
    password: 'dhr_data_anlaysis',
    database: 'dhr_data_anlaysis',
    charset: 'utf8mb4'
};

// CSV解析函数
function parseCSV(filePath) {
    console.log(`正在解析 ${filePath}...`);
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.trim().split('\n');
    const headers = lines[0].split(',');
    
    const data = lines.slice(1).map(line => {
        const values = [];
        let current = '';
        let inQuotes = false;
        
        for (let i = 0; i < line.length; i++) {
            const char = line[i];
            if (char === '"' && (i === 0 || line[i-1] === ',')) {
                inQuotes = true;
            } else if (char === '"' && inQuotes && (i === line.length - 1 || line[i+1] === ',')) {
                inQuotes = false;
            } else if (char === ',' && !inQuotes) {
                values.push(current);
                current = '';
            } else {
                current += char;
            }
        }
        values.push(current);
        
        const obj = {};
        headers.forEach((header, index) => {
            let value = values[index] || '';
            // 处理空值
            if (value === '' || value === 'null') {
                value = null;
            }
            // 处理布尔值
            else if (value === 'true') {
                value = true;
            } else if (value === 'false') {
                value = false;
            }
            // 处理数字
            else if (!isNaN(value) && value !== '') {
                value = parseFloat(value);
            }
            obj[header] = value;
        });
        return obj;
    });
    
    console.log(`解析完成，共 ${data.length} 条记录`);
    return data;
}

// 批量插入数据
async function insertData(connection, tableName, data, batchSize = 1000) {
    if (data.length === 0) return;
    
    console.log(`开始导入 ${tableName} 表，共 ${data.length} 条记录...`);
    
    const columns = Object.keys(data[0]);
    const placeholders = columns.map(() => '?').join(',');
    const sql = `INSERT INTO ${tableName} (${columns.join(',')}) VALUES (${placeholders})`;
    
    let imported = 0;
    for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize);
        const values = batch.map(row => columns.map(col => row[col]));
        
        try {
            await connection.query(sql, values[0]); // 先插入一条测试
            // 如果成功，继续批量插入
            for (let j = 1; j < values.length; j++) {
                await connection.query(sql, values[j]);
            }
            imported += batch.length;
            console.log(`已导入 ${imported}/${data.length} 条记录`);
        } catch (error) {
            console.error(`导入 ${tableName} 时出错:`, error.message);
            // 尝试逐条插入
            for (const row of batch) {
                try {
                    const rowValues = columns.map(col => row[col]);
                    await connection.query(sql, rowValues);
                    imported++;
                } catch (rowError) {
                    console.error(`跳过错误记录:`, rowError.message, row);
                }
            }
        }
    }
    
    console.log(`${tableName} 表导入完成，成功导入 ${imported} 条记录\n`);
}

// 主函数
async function importAllData() {
    let connection;
    
    try {
        console.log('=== 李宁劳动力预测数据导入工具 ===\n');
        console.log('连接数据库...');
        connection = await mysql.createConnection(dbConfig);
        console.log('数据库连接成功！\n');
        
        // 检查表是否存在，如果不存在则创建
        console.log('检查并创建数据表...');
        const createTableSQL = fs.readFileSync('./output/create_tables.sql', 'utf8');
        const statements = createTableSQL.split(';').filter(stmt => stmt.trim());
        
        for (const statement of statements) {
            if (statement.trim()) {
                try {
                    await connection.execute(statement);
                } catch (error) {
                    if (!error.message.includes('already exists')) {
                        console.error('创建表时出错:', error.message);
                    }
                }
            }
        }
        console.log('数据表检查完成！\n');
        
        // 定义导入顺序（按依赖关系）
        const importOrder = [
            { file: 'store.csv', table: 'lining_labor_store' },
            { file: 'product.csv', table: 'lining_labor_product' },
            { file: 'employee.csv', table: 'lining_labor_employee' },
            { file: 'member.csv', table: 'lining_labor_member' },
            { file: 'promotion.csv', table: 'lining_labor_promotion' },
            { file: 'schedule.csv', table: 'lining_labor_schedule' },
            { file: 'attendance.csv', table: 'lining_labor_attendance' },
            { file: 'traffic.csv', table: 'lining_labor_traffic' },
            { file: 'transaction.csv', table: 'lining_labor_transaction' },
            { file: 'forecast_history.csv', table: 'lining_labor_forecast_history' }
        ];
        
        // 按顺序导入数据
        for (const { file, table } of importOrder) {
            const filePath = path.join('./output', file);
            if (fs.existsSync(filePath)) {
                const data = parseCSV(filePath);
                await insertData(connection, table, data);
            } else {
                console.log(`文件 ${file} 不存在，跳过导入`);
            }
        }
        
        // 验证导入结果
        console.log('=== 导入结果验证 ===');
        for (const { table } of importOrder) {
            try {
                const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
                console.log(`${table}: ${rows[0].count.toLocaleString()} 条记录`);
            } catch (error) {
                console.log(`${table}: 查询失败 - ${error.message}`);
            }
        }
        
        console.log('\n✅ 数据导入完成！');
        
    } catch (error) {
        console.error('❌ 导入过程中出现错误:', error.message);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('数据库连接已关闭');
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    importAllData().catch(console.error);
}

module.exports = { importAllData };
